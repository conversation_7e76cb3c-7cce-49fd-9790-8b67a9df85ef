import * as aws from '@pulumi/aws';
import * as fs from 'fs';
import * as path from 'path';
import {
  getGlobalDynamoPermissions,
  getGlobalQueuePermissions,
  sshPublicKey,
  stageName,
} from './config';

export interface EC2ResourcesOptimized {
  instance: aws.ec2.Instance;
  securityGroup: aws.ec2.SecurityGroup;
  keyPair?: aws.ec2.KeyPair;
}

export function createEC2InfrastructureOptimized({
  fixtureDetailTable,
}: {
  fixtureDetailTable: aws.dynamodb.Table;
}): EC2ResourcesOptimized {
  // Get default VPC and subnets
  const defaultVpcPromise = aws.ec2.getVpc({ default: true });
  const defaultSubnetsPromise = defaultVpcPromise.then((vpc) =>
    aws.ec2.getSubnets({
      filters: [
        { name: 'vpc-id', values: [vpc.id] },
        { name: 'default-for-az', values: ['true'] },
      ],
    })
  );

  // Create security group for the EC2 instance (no ALB needed)
  const securityGroup = new aws.ec2.SecurityGroup(`${stageName}-monolith-sg-optimized`, {
    description: 'Security group for cost-optimized monolith (no ALB)',
    vpcId: defaultVpcPromise.then((vpc) => vpc.id),

    ingress: [
      {
        description: 'HTTP from CloudFront',
        fromPort: 3000,
        toPort: 3000,
        protocol: 'tcp',
        cidrBlocks: ['0.0.0.0/0'], // CloudFront IPs vary, so allow all (app handles auth)
      },
      {
        description: 'SSH',
        fromPort: 22,
        toPort: 22,
        protocol: 'tcp',
        cidrBlocks: ['0.0.0.0/0'], // Restrict this in production if needed
      },
    ],

    egress: [
      {
        description: 'All outbound traffic',
        fromPort: 0,
        toPort: 0,
        protocol: '-1',
        cidrBlocks: ['0.0.0.0/0'],
      },
    ],

    tags: {
      Name: `${stageName}-monolith-sg-optimized`,
    },
  });

  // Optionally create key pair for SSH access if provided via config
  let keyPair: aws.ec2.KeyPair | undefined;
  if (sshPublicKey) {
    keyPair = new aws.ec2.KeyPair(`${stageName}-monolith-key`, {
      keyName: `${stageName}-monolith-key`,
      publicKey: sshPublicKey,
      tags: { Name: `${stageName}-monolith-key` },
    });
  }

  // Get the latest Amazon Linux 2023 AMI
  const amiPromise = aws.ec2.getAmi({
    mostRecent: true,
    owners: ['amazon'],
    filters: [
      { name: 'name', values: ['al2023-ami-*-arm64'] },
      { name: 'virtualization-type', values: ['hvm'] },
    ],
  });

  // Shared user data script
  const userDataScriptPath = path.join(__dirname, 'user-data', 'monolith-base.sh');
  const userData = fs
    .readFileSync(userDataScriptPath, 'utf8')
    .replace(/__STAGE_NAME__/g, stageName);

  // Create IAM role for EC2 instance (same permissions as before)
  const instanceRole = new aws.iam.Role(`${stageName}-monolith-instance-role-optimized`, {
    assumeRolePolicy: JSON.stringify({
      Version: '2012-10-17',
      Statement: [
        {
          Action: 'sts:AssumeRole',
          Effect: 'Allow',
          Principal: {
            Service: 'ec2.amazonaws.com',
          },
        },
      ],
    }),
  });

  // Explicit policy for fixtureDetailTable (read/write)
  new aws.iam.RolePolicy(`${stageName}-monolith-fixture-detail-policy-optimized`, {
    role: instanceRole.id,
    policy: fixtureDetailTable.arn.apply((arn) =>
      JSON.stringify({
        Version: '2012-10-17',
        Statement: [
          {
            Effect: 'Allow',
            Action: [
              'dynamodb:GetItem',
              'dynamodb:Query',
              'dynamodb:Scan',
              'dynamodb:BatchGetItem',
              'dynamodb:PutItem',
              'dynamodb:UpdateItem',
              'dynamodb:DeleteItem',
              'dynamodb:BatchWriteItem',
            ],
            Resource: [arn, `${arn}/index/*`],
          },
        ],
      })
    ),
  });

  // Attach CloudWatch agent policy
  new aws.iam.RolePolicyAttachment(`${stageName}-monolith-cloudwatch-policy-optimized`, {
    role: instanceRole.name,
    policyArn: 'arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy',
  });

  // Add comprehensive policy for Cognito and DynamoDB access (same as before)
  new aws.iam.RolePolicy(`${stageName}-monolith-comprehensive-policy-optimized`, {
    role: instanceRole.id,
    policy: JSON.stringify({
      Version: '2012-10-17',
      Statement: [
        {
          Effect: 'Allow',
          Action: [
            'cognito-idp:InitiateAuth',
            'cognito-idp:RespondToAuthChallenge',
            'cognito-idp:GetUser',
            'cognito-idp:AdminGetUser',
            'cognito-idp:AdminCreateUser',
            'cognito-idp:AdminSetUserPassword',
            'cognito-idp:AdminUpdateUserAttributes',
            'cognito-idp:ListUsers',
            'cognito-identity:GetId',
            'cognito-identity:GetCredentialsForIdentity',
          ],
          Resource: '*',
        },
        {
          Effect: 'Allow',
          Action: [
            'xray:PutTraceSegments',
            'xray:PutTelemetryRecords',
            'xray:GetSamplingRules',
            'xray:GetSamplingTargets',
            'xray:GetSamplingStatisticSummaries',
          ],
          Resource: '*',
        },
        {
          Effect: 'Allow',
          Action: [
            'logs:CreateLogGroup',
            'logs:CreateLogStream',
            'logs:PutLogEvents',
            'logs:DescribeLogStreams',
            'logs:DescribeLogGroups',
          ],
          Resource: '*',
        },
      ],
    }),
  });

  // Add global DynamoDB and SQS permissions (same as before)
  const globalDynamoPerms = getGlobalDynamoPermissions();
  globalDynamoPerms.forEach((permission) => {
    const actions: string[] = [];
    if (permission.permissions === 'read' || permission.permissions === 'both') {
      actions.push('dynamodb:GetItem', 'dynamodb:Query', 'dynamodb:Scan', 'dynamodb:BatchGetItem');
    }
    if (permission.permissions === 'write' || permission.permissions === 'both') {
      actions.push(
        'dynamodb:PutItem',
        'dynamodb:UpdateItem',
        'dynamodb:DeleteItem',
        'dynamodb:BatchWriteItem'
      );
    }
    if (actions.length > 0) {
      new aws.iam.RolePolicy(`${stageName}-monolith-dynamo-${permission.name}-policy-optimized`, {
        role: instanceRole.id,
        policy: permission.table.arn.apply((arn) =>
          JSON.stringify({
            Version: '2012-10-17',
            Statement: [{ Effect: 'Allow', Action: actions, Resource: [arn, `${arn}/index/*`] }],
          })
        ),
      });
    }
  });

  const globalQueuePerms = getGlobalQueuePermissions();
  globalQueuePerms.forEach((permission) => {
    const actions: string[] = [];
    if (permission.permissions === 'send' || permission.permissions === 'both') {
      actions.push('sqs:SendMessage', 'sqs:GetQueueAttributes');
    }
    if (permission.permissions === 'read' || permission.permissions === 'both') {
      actions.push(
        'sqs:ReceiveMessage',
        'sqs:DeleteMessage',
        'sqs:GetQueueAttributes',
        'sqs:ChangeMessageVisibility'
      );
    }
    if (actions.length > 0) {
      new aws.iam.RolePolicy(`${stageName}-monolith-sqs-${permission.name}-policy-optimized`, {
        role: instanceRole.id,
        policy: permission.queue.arn.apply((arn) =>
          JSON.stringify({
            Version: '2012-10-17',
            Statement: [{ Effect: 'Allow', Action: actions, Resource: arn }],
          })
        ),
      });
    }
  });

  // Create instance profile
  const instanceProfile = new aws.iam.InstanceProfile(
    `${stageName}-monolith-instance-profile-optimized`,
    {
      role: instanceRole.name,
    }
  );

  // Create EC2 instance (cost-optimized)
  const instance = new aws.ec2.Instance(`${stageName}-monolith-instance-optimized`, {
    instanceType: 't4g.micro', // ARM-based, cost-effective
    ami: amiPromise.then((ami) => ami.id),
    keyName: keyPair?.keyName,
    vpcSecurityGroupIds: [securityGroup.id],
    subnetId: defaultSubnetsPromise.then((subnets) => subnets.ids[0]),
    iamInstanceProfile: instanceProfile.name,
    userData: userData,
    rootBlockDevice: {
      volumeSize: 30, // GiB
      volumeType: 'gp3',
      deleteOnTermination: true,
    },
    tags: {
      Name: `${stageName}-monolith-instance-optimized`,
      CostOptimized: 'true',
    },
  });

  return {
    instance,
    securityGroup,
    keyPair,
  };
}
