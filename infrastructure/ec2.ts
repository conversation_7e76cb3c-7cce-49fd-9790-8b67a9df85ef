import * as aws from '@pulumi/aws';
import * as fs from 'fs';
import * as path from 'path';
import {
  getGlobalDynamoPermissions,
  getGlobalQueuePermissions,
  sshPublicKey,
  stageName,
} from './config';

export interface EC2Resources {
  instance: aws.ec2.Instance;
  securityGroup: aws.ec2.SecurityGroup;
  applicationLoadBalancer: aws.lb.LoadBalancer;
  targetGroup: aws.lb.TargetGroup;
  listener: aws.lb.Listener;
  keyPair?: aws.ec2.KeyPair; // optional now
}

export function createEC2Infrastructure({
  fixtureDetailTable,
}: {
  fixtureDetailTable: aws.dynamodb.Table;
}): EC2Resources {
  // Get default VPC and subnets
  const defaultVpcPromise = aws.ec2.getVpc({ default: true });
  const defaultSubnetsPromise = defaultVpcPromise.then((vpc) =>
    aws.ec2.getSubnets({
      filters: [
        { name: 'vpc-id', values: [vpc.id] },
        { name: 'default-for-az', values: ['true'] },
      ],
    })
  );

  // Create security group for the EC2 instance
  const securityGroup = new aws.ec2.SecurityGroup(`${stageName}-monolith-sg`, {
    description: 'Security group for monolith API server',
    vpcId: defaultVpcPromise.then((vpc) => vpc.id),
    ingress: [
      {
        description: 'HTTP from ALB',
        fromPort: 3000,
        toPort: 3000,
        protocol: 'tcp',
        securityGroups: [], // Will be set after ALB security group is created
      },
      {
        description: 'SSH',
        fromPort: 22,
        toPort: 22,
        protocol: 'tcp',
        cidrBlocks: ['0.0.0.0/0'], // Restrict this in production
      },
    ],
    egress: [
      {
        description: 'All outbound traffic',
        fromPort: 0,
        toPort: 0,
        protocol: '-1',
        cidrBlocks: ['0.0.0.0/0'],
      },
    ],
    tags: { Name: `${stageName}-monolith-sg` },
  });

  // Create security group for the Application Load Balancer
  const albSecurityGroup = new aws.ec2.SecurityGroup(`${stageName}-alb-sg`, {
    description: 'Security group for Application Load Balancer',
    vpcId: defaultVpcPromise.then((vpc) => vpc.id),
    ingress: [
      { description: 'HTTP', fromPort: 80, toPort: 80, protocol: 'tcp', cidrBlocks: ['0.0.0.0/0'] },
      {
        description: 'HTTPS',
        fromPort: 443,
        toPort: 443,
        protocol: 'tcp',
        cidrBlocks: ['0.0.0.0/0'],
      },
    ],
    egress: [
      {
        description: 'All outbound traffic',
        fromPort: 0,
        toPort: 0,
        protocol: '-1',
        cidrBlocks: ['0.0.0.0/0'],
      },
    ],
    tags: { Name: `${stageName}-alb-sg` },
  });

  // Update the EC2 security group to allow traffic from ALB
  new aws.ec2.SecurityGroupRule(`${stageName}-monolith-sg-alb-rule`, {
    type: 'ingress',
    fromPort: 3000,
    toPort: 3000,
    protocol: 'tcp',
    sourceSecurityGroupId: albSecurityGroup.id,
    securityGroupId: securityGroup.id,
  });

  // Optionally create key pair for SSH access if provided via config
  let keyPair: aws.ec2.KeyPair | undefined;
  if (sshPublicKey) {
    keyPair = new aws.ec2.KeyPair(`${stageName}-monolith-key`, {
      keyName: `${stageName}-monolith-key`,
      publicKey: sshPublicKey,
      tags: { Name: `${stageName}-monolith-key` },
    });
  }

  // Get the latest Amazon Linux 2023 AMI
  const amiPromise = aws.ec2.getAmi({
    mostRecent: true,
    owners: ['amazon'],
    filters: [
      { name: 'name', values: ['al2023-ami-*-arm64'] },
      { name: 'virtualization-type', values: ['hvm'] },
    ],
  });

  // Load shared user data script (replace stage placeholder)
  const userDataScriptPath = path.join(__dirname, 'user-data', 'monolith-base.sh');
  const userData = fs
    .readFileSync(userDataScriptPath, 'utf8')
    .replace(/__STAGE_NAME__/g, stageName);

  // Create IAM role for EC2 instance
  const instanceRole = new aws.iam.Role(`${stageName}-monolith-instance-role`, {
    assumeRolePolicy: JSON.stringify({
      Version: '2012-10-17',
      Statement: [
        {
          Action: 'sts:AssumeRole',
          Effect: 'Allow',
          Principal: {
            Service: 'ec2.amazonaws.com',
          },
        },
      ],
    }),
  });

  // Attach CloudWatch agent policy
  new aws.iam.RolePolicyAttachment(`${stageName}-monolith-cloudwatch-policy`, {
    role: instanceRole.name,
    policyArn: 'arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy',
  });

  // Add comprehensive policy for Cognito and DynamoDB access (same as Lambda functions)
  new aws.iam.RolePolicy(`${stageName}-monolith-comprehensive-policy`, {
    role: instanceRole.id,
    policy: JSON.stringify({
      Version: '2012-10-17',
      Statement: [
        // Cognito permissions for authentication
        {
          Effect: 'Allow',
          Action: [
            'cognito-idp:InitiateAuth',
            'cognito-idp:RespondToAuthChallenge',
            'cognito-idp:GetUser',
            'cognito-idp:AdminGetUser',
            'cognito-idp:AdminCreateUser',
            'cognito-idp:AdminSetUserPassword',
            'cognito-idp:AdminUpdateUserAttributes',
            'cognito-idp:ListUsers',
            'cognito-identity:GetId',
            'cognito-identity:GetCredentialsForIdentity',
          ],
          Resource: '*',
        },
        // X-Ray permissions (same as Lambda)
        {
          Effect: 'Allow',
          Action: [
            'xray:PutTraceSegments',
            'xray:PutTelemetryRecords',
            'xray:GetSamplingRules',
            'xray:GetSamplingTargets',
            'xray:GetSamplingStatisticSummaries',
          ],
          Resource: '*',
        },
        // CloudWatch Logs permissions
        {
          Effect: 'Allow',
          Action: [
            'logs:CreateLogGroup',
            'logs:CreateLogStream',
            'logs:PutLogEvents',
            'logs:DescribeLogStreams',
            'logs:DescribeLogGroups',
          ],
          Resource: '*',
        },
      ],
    }),
  });

  // Add global DynamoDB permissions (same as Lambda functions)
  const globalDynamoPerms = getGlobalDynamoPermissions();
  globalDynamoPerms.forEach((permission) => {
    const actions: string[] = [];

    if (permission.permissions === 'read' || permission.permissions === 'both') {
      actions.push('dynamodb:GetItem', 'dynamodb:Query', 'dynamodb:Scan', 'dynamodb:BatchGetItem');
    }

    if (permission.permissions === 'write' || permission.permissions === 'both') {
      actions.push(
        'dynamodb:PutItem',
        'dynamodb:UpdateItem',
        'dynamodb:DeleteItem',
        'dynamodb:BatchWriteItem'
      );
    }

    if (actions.length > 0) {
      new aws.iam.RolePolicy(`${stageName}-monolith-dynamo-${permission.name}-policy`, {
        role: instanceRole.id,
        policy: permission.table.arn.apply((arn) =>
          JSON.stringify({
            Version: '2012-10-17',
            Statement: [
              {
                Effect: 'Allow',
                Action: actions,
                Resource: [
                  arn, // Table ARN
                  `${arn}/index/*`, // Index ARNs
                ],
              },
            ],
          })
        ),
      });
    }
  });

  // Add global SQS permissions (same as Lambda functions)
  const globalQueuePerms = getGlobalQueuePermissions();
  globalQueuePerms.forEach((permission) => {
    const actions: string[] = [];

    if (permission.permissions === 'send' || permission.permissions === 'both') {
      actions.push('sqs:SendMessage', 'sqs:GetQueueAttributes');
    }

    if (permission.permissions === 'read' || permission.permissions === 'both') {
      actions.push(
        'sqs:ReceiveMessage',
        'sqs:DeleteMessage',
        'sqs:GetQueueAttributes',
        'sqs:ChangeMessageVisibility'
      );
    }

    if (actions.length > 0) {
      new aws.iam.RolePolicy(`${stageName}-monolith-sqs-${permission.name}-policy`, {
        role: instanceRole.id,
        policy: permission.queue.arn.apply((arn) =>
          JSON.stringify({
            Version: '2012-10-17',
            Statement: [
              {
                Effect: 'Allow',
                Action: actions,
                Resource: arn,
              },
            ],
          })
        ),
      });
    }
  });

  // Explicit policy for fixtureDetailTable (read/write)
  new aws.iam.RolePolicy(`${stageName}-monolith-fixture-detail-policy`, {
    role: instanceRole.id,
    policy: fixtureDetailTable.arn.apply((arn) =>
      JSON.stringify({
        Version: '2012-10-17',
        Statement: [
          {
            Effect: 'Allow',
            Action: [
              'dynamodb:GetItem',
              'dynamodb:Query',
              'dynamodb:Scan',
              'dynamodb:BatchGetItem',
              'dynamodb:PutItem',
              'dynamodb:UpdateItem',
              'dynamodb:DeleteItem',
              'dynamodb:BatchWriteItem',
            ],
            Resource: [arn, `${arn}/index/*`],
          },
        ],
      })
    ),
  });

  // Create instance profile
  const instanceProfile = new aws.iam.InstanceProfile(`${stageName}-monolith-instance-profile`, {
    role: instanceRole.name,
  });

  // Create EC2 instance
  const instance = new aws.ec2.Instance(`${stageName}-monolith-instance`, {
    instanceType: 't4g.micro',
    ami: amiPromise.then((ami) => ami.id),
    keyName: keyPair?.keyName,
    vpcSecurityGroupIds: [securityGroup.id],
    subnetId: defaultSubnetsPromise.then((subnets) => subnets.ids[0]),
    iamInstanceProfile: instanceProfile.name,
    userData: userData,
    tags: { Name: `${stageName}-monolith-instance` },
    rootBlockDevice: {
      volumeSize: 12, // GiB
      volumeType: 'gp3',
      deleteOnTermination: true,
    },
  });

  // Create Application Load Balancer
  const applicationLoadBalancer = new aws.lb.LoadBalancer(`${stageName}-monolith-alb`, {
    loadBalancerType: 'application',
    securityGroups: [albSecurityGroup.id],
    subnets: defaultSubnetsPromise.then((subnets) => subnets.ids),
    tags: { Name: `${stageName}-monolith-alb` },
  });

  // Create target group
  const targetGroup = new aws.lb.TargetGroup(`${stageName}-monolith-tg`, {
    port: 3000,
    protocol: 'HTTP',
    vpcId: defaultVpcPromise.then((vpc) => vpc.id),
    targetType: 'instance',
    healthCheck: {
      enabled: true,
      path: '/ping',
      port: '3000',
      protocol: 'HTTP',
      healthyThreshold: 2,
      unhealthyThreshold: 2,
      timeout: 5,
      interval: 30,
      matcher: '200',
    },
    tags: { Name: `${stageName}-monolith-tg` },
  });

  // Attach instance to target group
  new aws.lb.TargetGroupAttachment(`${stageName}-monolith-tg-attachment`, {
    targetGroupArn: targetGroup.arn,
    targetId: instance.id,
    port: 3000,
  });

  // Create listener for the load balancer
  const listener = new aws.lb.Listener(`${stageName}-monolith-listener`, {
    loadBalancerArn: applicationLoadBalancer.arn,
    port: 80,
    protocol: 'HTTP',
    defaultActions: [{ type: 'forward', targetGroupArn: targetGroup.arn }],
  });

  return { instance, securityGroup, applicationLoadBalancer, targetGroup, listener, keyPair };
}
