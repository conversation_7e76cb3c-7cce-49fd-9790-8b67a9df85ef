#!/bin/bash
# Monolith EC2 base provisioning script (shared by standard and cost-optimized stacks)
# This script is intentionally idempotent-ish for initial boot use only.
# Stage placeholder: __STAGE_NAME__

# Use less strict error handling to prevent SSH connectivity issues
set -uo pipefail

LOG_FILE=/var/log/monolith-userdata.log
exec >> $LOG_FILE 2>&1

echo "[user-data] Starting provisioning at $(date -u +%Y-%m-%dT%H:%M:%SZ)"

# Ensure SSH service is running and enabled (safety measure)
systemctl enable sshd || true
systemctl start sshd || true

# 1. System packages & Node.js
if ! command -v node >/dev/null 2>&1; then
  echo "[user-data] Installing system packages and Node.js..."
  yum update -y || true
  yum install -y git amazon-cloudwatch-agent epel-release || true
  yum install -y cpulimit || true  # For CPU limiting during deployments
  curl -fsSL https://rpm.nodesource.com/setup_20.x | bash - || true
  yum install -y nodejs || true
else
  echo "[user-data] Node.js already installed"
fi
node -v || echo "[user-data] Node.js version check failed"

# 2. CloudWatch agent config (comprehensive)
echo "[user-data] Configuring CloudWatch agent..."
mkdir -p /opt/aws/amazon-cloudwatch-agent/etc || true
cat > /opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json <<'EOF'
{
  "agent": { "metrics_collection_interval": 60, "run_as_user": "cwagent" },
  "metrics": {
    "namespace": "CWAgent",
    "metrics_collected": {
      "cpu": { "measurement": ["cpu_usage_idle","cpu_usage_iowait","cpu_usage_user","cpu_usage_system"], "metrics_collection_interval": 60, "totalcpu": false },
      "disk": { "measurement": ["used_percent"], "metrics_collection_interval": 60, "resources": ["*"] },
      "diskio": { "measurement": ["io_time"], "metrics_collection_interval": 60, "resources": ["*"] },
      "mem": { "measurement": ["mem_used_percent"], "metrics_collection_interval": 60 }
    }
  },
  "logs": { "logs_collected": { "files": { "collect_list": [ { "file_path": "/var/log/monolith.log", "log_group_name": "/aws/ec2/__STAGE_NAME__-monolith", "log_stream_name": "{instance_id}/application", "timezone": "UTC", "retention_in_days": 7 } ] } } }
}
EOF
if /opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl -a fetch-config -m ec2 -c file:/opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json -s; then
  echo "[user-data] CloudWatch agent configured successfully"
else
  echo "[user-data] CloudWatch agent configuration failed (non-fatal)"
fi

# 3. Log rotation
cat > /etc/logrotate.d/monolith <<'EOF'
/var/log/monolith.log {
  daily
  rotate 7
  compress
  delaycompress
  missingok
  notifempty
  create 644 ec2-user ec2-user
  postrotate
    systemctl reload monolith || true
  endscript
}
EOF

# 4. Placeholder release structure for atomic deployments
echo "[user-data] Setting up atomic deployment structure..."
BASE_DIR=/opt/monolith
RELEASES_DIR=$BASE_DIR/releases
SHARED_DIR=$BASE_DIR/shared
mkdir -p "$RELEASES_DIR/placeholder/monolith" "$SHARED_DIR" || true
if [ ! -f "$RELEASES_DIR/placeholder/monolith/server.js" ]; then
  echo "[user-data] Creating placeholder server..."
  cat > "$RELEASES_DIR/placeholder/monolith/server.js" <<'APP'
const http = require('http');
const server = http.createServer((req, res) => {
  res.writeHead(200, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify({
    placeholder: true,
    path: req.url,
    timestamp: new Date().toISOString(),
    message: 'Placeholder monolith server - waiting for deployment'
  }));
});
server.listen(3000, () => {
  console.log('Placeholder monolith listening on port 3000');
});
APP
else
  echo "[user-data] Placeholder server already exists"
fi
ln -sfn "$RELEASES_DIR/placeholder" "$BASE_DIR/current" || true
chown -R ec2-user:ec2-user "$BASE_DIR" || true
echo "[user-data] Atomic deployment structure ready"

# 5. systemd service (idempotent overwrite ok)
echo "[user-data] Creating systemd service..."
cat > /etc/systemd/system/monolith.service <<'EOF'
[Unit]
Description=Jumpers for Goalposts Monolith API
After=network.target

[Service]
Type=simple
User=ec2-user
WorkingDirectory=/opt/monolith/current
ExecStart=/usr/bin/node monolith/server.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production
Environment=PORT=3000
StandardOutput=append:/var/log/monolith.log
StandardError=append:/var/log/monolith.log

[Install]
WantedBy=multi-user.target
EOF
echo "[user-data] Systemd service file created"

systemctl daemon-reload
systemctl enable monolith || true

# Start the service and verify it's working
echo "[user-data] Starting monolith service..."
if systemctl start monolith; then
  echo "[user-data] Monolith service started successfully"
  # Wait a moment and check if it's still running
  sleep 3
  if systemctl is-active --quiet monolith; then
    echo "[user-data] Monolith service is running and healthy"
  else
    echo "[user-data] WARNING: Monolith service started but is not active"
    systemctl status monolith --no-pager || true
  fi
else
  echo "[user-data] ERROR: Failed to start monolith service"
  systemctl status monolith --no-pager || true
  # Don't fail the entire user-data script, just log the error
fi

echo "[user-data] Provisioning complete at $(date -u +%Y-%m-%dT%H:%M:%SZ)"
