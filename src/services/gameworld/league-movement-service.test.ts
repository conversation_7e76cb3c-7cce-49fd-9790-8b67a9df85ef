import { AvailableTeam } from '@/entities/AvailableTeam.ts';
import { LeagueMovementService } from '@/services/gameworld/league-movement-service.js';
import { GameworldFactory } from '@/testing/factories/gameworldFactory.js';
import { LeagueFactory } from '@/testing/factories/leagueFactory.js';
import { ManagerFactory } from '@/testing/factories/managerFactory.js';
import { TeamsFactory } from '@/testing/factories/teamFactory.js';
import {
  createMockRepositories,
  mockEntityManager,
  mockGameworldRepository,
  mockLeagueRepository,
} from '@/testing/mockRepositories.js';
import { raw } from '@mikro-orm/core';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock the LeagueProcessor
vi.mock('@/functions/league/logic/LeagueProcessorV2.js', () => ({
  LeagueProcessor: {
    sortTeamsInLeague: vi.fn().mockImplementation((teams) => teams),
    processPromotionsAndRelegations: vi.fn().mockReturnValue([
      {
        teamId: 'team-1',
        fromLeagueId: 'league-1',
        toLeagueId: 'league-2',
      },
    ]),
    getTeamStandings: vi.fn().mockReturnValue({
      points: 10,
      goalsFor: 5,
      goalsAgainst: 2,
    }),
  },
}));

describe('LeagueMovementService', () => {
  let service: LeagueMovementService;
  let repositories: ReturnType<typeof createMockRepositories>;

  beforeEach(() => {
    repositories = createMockRepositories();
    service = new LeagueMovementService(repositories);
  });

  describe('prepareLeagueMovementData', () => {
    it('should successfully prepare league movement data', async () => {
      // Arrange
      const gameworldId = 'gameworld-1';
      const teams = [
        TeamsFactory.build({ teamId: 'team-1', gameworldId }),
        TeamsFactory.build({ teamId: 'team-2', gameworldId }),
      ];

      const leagues = [
        LeagueFactory.build({
          id: 'league-1',
          teams: {
            getItems: () => teams,
            length: teams.length,
          } as any,
        }),
      ];

      mockLeagueRepository.getLeaguesByGameworld.mockResolvedValue(leagues);

      // Act
      const result = await service.prepareLeagueMovementData(gameworldId);

      // Assert
      expect(result).toBeDefined();
      expect(result.gameworldId).toBe(gameworldId);
      expect(result.leagues).toEqual(leagues);
      expect(result.allTeams).toHaveLength(2);
      expect(result.movements).toHaveLength(1);
      expect(result.sortedLeagues.size).toBe(1);
    });

    it('should throw error when no leagues found', async () => {
      // Arrange
      const gameworldId = 'gameworld-1';
      mockLeagueRepository.getLeaguesByGameworld.mockResolvedValue([]);

      // Act & Assert
      await expect(service.prepareLeagueMovementData(gameworldId)).rejects.toThrow(
        'No leagues found for gameworld: gameworld-1'
      );
    });

    it('should throw error when no teams found', async () => {
      // Arrange
      const gameworldId = 'gameworld-1';
      const leagues = [
        LeagueFactory.build({
          id: 'league-1',
          teams: {
            getItems: () => [],
            length: 0,
          } as any,
        }),
      ];

      mockLeagueRepository.getLeaguesByGameworld.mockResolvedValue(leagues);

      // Act & Assert
      await expect(service.prepareLeagueMovementData(gameworldId)).rejects.toThrow(
        'No teams found for gameworld: gameworld-1'
      );
    });
  });

  describe('awardPrizeMoney', () => {
    it('should award prize money to teams based on league position', async () => {
      // Arrange
      const mockEm = {
        nativeUpdate: vi.fn(),
      } as any;

      const teams = [
        TeamsFactory.build({ teamId: 'team-1', gameworldId: 'gw-1' }),
        TeamsFactory.build({ teamId: 'team-2', gameworldId: 'gw-1' }),
      ];

      const sortedLeagues = new Map([['league-1', teams]]);

      const leagues = [
        {
          id: 'league-1',
          leagueRules: {
            minimumPrize: 1000,
            maximumPrize: 5000,
            teamCount: 2,
          },
        },
      ];

      // Act
      await service.awardPrizeMoney(mockEm, sortedLeagues, leagues);

      // Assert
      expect(mockEm.nativeUpdate).toHaveBeenCalledTimes(2);
      expect(mockEm.nativeUpdate).toHaveBeenCalledWith(
        expect.anything(),
        { teamId: 'team-1', gameworldId: 'gw-1' },
        { balance: raw(`balance + 5000`) }
      );
      expect(mockEm.nativeUpdate).toHaveBeenCalledWith(
        expect.anything(),
        { teamId: 'team-2', gameworldId: 'gw-1' },
        { balance: raw(`balance + 1000`) }
      );
    });

    it('should skip leagues without league rules', async () => {
      // Arrange
      const mockEm = {
        nativeUpdate: vi.fn(),
      } as any;

      const teams = [TeamsFactory.build()];
      const sortedLeagues = new Map([['league-1', teams]]);
      const leagues = [{ id: 'league-1', leagueRules: null }];

      // Act
      await service.awardPrizeMoney(mockEm, sortedLeagues, leagues);

      // Assert
      expect(mockEm.nativeUpdate).not.toHaveBeenCalled();
    });
  });

  describe('manageAvailableTeams', () => {
    it('should add teams relegated into manageable tier', async () => {
      // Arrange
      const mockEm = {
        nativeDelete: vi.fn(),
        find: vi.fn().mockResolvedValue([]),
        insertMany: vi.fn(),
      } as any;

      const gameworld = GameworldFactory.build({
        id: 'gw-1',
        highestManageableTier: 3,
      });

      mockGameworldRepository.getGameworld.mockResolvedValue(gameworld);

      const teams = [
        TeamsFactory.build({
          teamId: 'team-1',
          manager: undefined, // No manager
        }),
      ];

      const movements = [
        {
          teamId: 'team-1',
          fromLeagueId: 'league-1',
          toLeagueId: 'league-2',
        },
      ];

      const leagues = [
        { id: 'league-1', tier: 2 }, // Non-manageable
        { id: 'league-2', tier: 3 }, // Manageable
      ];

      // Act
      await service.manageAvailableTeams(mockEm, 'gw-1', movements, teams, leagues);

      // Assert
      expect(mockEm.insertMany).toHaveBeenCalledWith(
        AvailableTeam,
        expect.arrayContaining([
          expect.objectContaining({
            gameworldId: 'gw-1',
            teamId: 'team-1',
          }),
        ])
      );
    });

    it('should remove teams promoted above manageable tier', async () => {
      // Arrange
      const mockEm = {
        nativeDelete: vi.fn(),
        find: vi.fn(),
        insertMany: vi.fn(),
      } as any;

      const gameworld = GameworldFactory.build({
        id: 'gw-1',
        highestManageableTier: 3,
      });

      mockGameworldRepository.getGameworld.mockResolvedValue(gameworld);

      const teams = [
        TeamsFactory.build({
          teamId: 'team-1',
          manager: undefined, // No manager
        }),
      ];

      const movements = [
        {
          teamId: 'team-1',
          fromLeagueId: 'league-1',
          toLeagueId: 'league-2',
        },
      ];

      const leagues = [
        { id: 'league-1', tier: 3 }, // Manageable
        { id: 'league-2', tier: 2 }, // Non-manageable
      ];

      // Act
      await service.manageAvailableTeams(mockEm, 'gw-1', movements, teams, leagues);

      // Assert
      expect(mockEm.nativeDelete).toHaveBeenCalledWith(AvailableTeam, {
        gameworldId: 'gw-1',
        teamId: { $in: ['team-1'] },
      });
    });

    it('should skip teams with managers', async () => {
      // Arrange
      const mockEm = {
        nativeDelete: vi.fn(),
        find: vi.fn(),
        insertMany: vi.fn(),
      } as any;

      const gameworld = GameworldFactory.build({
        id: 'gw-1',
        highestManageableTier: 3,
      });

      mockGameworldRepository.getGameworld.mockResolvedValue(gameworld);

      const manager = ManagerFactory.build({ managerId: 'manager-1' });
      const teams = [
        TeamsFactory.build({
          teamId: 'team-1',
          manager: manager, // Has manager
        }),
      ];

      const movements = [
        {
          teamId: 'team-1',
          fromLeagueId: 'league-1',
          toLeagueId: 'league-2',
        },
      ];

      const leagues = [
        { id: 'league-1', tier: 3 },
        { id: 'league-2', tier: 2 },
      ];

      // Act
      await service.manageAvailableTeams(mockEm, 'gw-1', movements, teams, leagues);

      // Assert
      expect(mockEm.nativeDelete).not.toHaveBeenCalled();
      expect(mockEm.insertMany).not.toHaveBeenCalled();
    });
  });

  describe('optimizeFixtureDeletion', () => {
    it('should delete completed fixtures and their match history for gameworld', async () => {
      // Arrange
      const fixtures = [{ fixtureId: 1 }, { fixtureId: 2 }, { fixtureId: 3 }];
      const mockFind = vi.fn().mockResolvedValueOnce(fixtures);
      const mockDelete = vi.fn()
        .mockResolvedValueOnce(5) // PlayerMatchHistory deletion count
        .mockResolvedValueOnce(3); // Fixture deletion count
      mockEntityManager.find.mockImplementation(mockFind);
      mockEntityManager.nativeDelete.mockImplementation(mockDelete);
      mockEntityManager.count.mockResolvedValueOnce(3);

      // Act
      await service.optimizeFixtureDeletion(mockEntityManager, 'gw-1');

      // Assert
      expect(mockFind).toHaveBeenCalledTimes(1);
      expect(mockDelete).toHaveBeenCalledTimes(2);
      // First call should delete PlayerMatchHistory
      expect(mockDelete).toHaveBeenNthCalledWith(1, expect.anything(), { fixtureId: { $in: [1, 2, 3] } });
      // Second call should delete Fixtures
      expect(mockDelete).toHaveBeenNthCalledWith(2, expect.anything(), { fixtureId: { $in: [1, 2, 3] } });
    });

    it('should not delete if no fixtures found', async () => {
      // Arrange
      const mockFind = vi.fn().mockResolvedValueOnce([]);
      const mockDelete = vi.fn();

      const mockEm = {
        find: mockFind,
        nativeDelete: mockDelete,
        count: vi.fn().mockResolvedValueOnce(0),
      } as any;

      // Act
      await service.optimizeFixtureDeletion(mockEm, 'gw-1');

      // Assert
      expect(mockFind).not.toHaveBeenCalled();
      expect(mockDelete).not.toHaveBeenCalled();
    });
  });
});
