import { Player } from '@/entities/Player.js';
import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { getUser } from '@/utils/getUser.js';
import { logger } from '@/utils/logger.js';
import { jsonStringifySafe } from '@/utils/misc.js';

/**
 * Path parameters for useMagicSponge API endpoint
 */
interface MagicSpongePathParameters {
  gameworldId: string;
  playerId: string;
}
/**
 * Lambda function to use a magic sponge on a player to heal injury
 * Player must belong to the user's team
 */
export const main = async function (event: HttpEvent<void, MagicSpongePathParameters, void>) {
  const { playerRepository, managerRepository } = event.context.repositories;

  // Get the current user ID
  const userId = getUser(event);
  if (!userId) {
    return buildResponse(401, JSON.stringify({ error: 'Unauthorized' }));
  }

  // Get path parameters
  const { gameworldId, playerId } = event.pathParameters;

  // Get the manager to find the team ID
  const manager = await managerRepository.getManagerById(userId);
  if (!manager || !manager.team) {
    return buildResponse(404, JSON.stringify({ error: 'Manager or team not found' }));
  }

  const teamId = manager.team.teamId;

  // Get the player
  let player: Player | null = null;
  try {
    const em = playerRepository.getEntityManager();
    player = await em.findOne(Player, { gameworldId, playerId });
  } catch (error) {
    logger.error('Failed to get player:', { error, gameworldId, playerId });
    throw error;
  }
  if (!player) {
    return buildResponse(404, JSON.stringify({ error: 'Player not found' }));
  }

  // Check if the player belongs to the user's team
  if (player.team?.teamId !== teamId) {
    return buildResponse(
      403,
      JSON.stringify({
        error: 'Forbidden',
        message: 'You can only use a magic sponge on players from your own team',
      })
    );
  }

  // Check if player is injured
  if (player.injuredUntil && player.injuredUntil > Date.now()) {
    // subtract 24 hours from injuredUntil
    player.injuredUntil -= BigInt(1000 * 60 * 60 * 24);
  } else {
    // set energy to 100
    player.energy = 100;
  }

  await managerRepository.updateMagicSpongeCount(manager.managerId, -1);
  await playerRepository.updatePlayer(player);

  return buildResponse(
    200,
    jsonStringifySafe({
      playerId: player.playerId,
      energy: player.energy,
      injuredUntil: player.injuredUntil,
    })
  );
};

export const handler = httpMiddify(main, {});
