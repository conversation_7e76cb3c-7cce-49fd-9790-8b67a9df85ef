import { getPlayerStatsSchema } from '@/functions/player/schema.js';
import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { jsonStringifySafe } from '@/utils/misc.js';

interface PathParameters {
  gameworldId: string;
  playerId: string;
}

export type GetPlayerStatsEvent = HttpEvent<void, PathParameters, void>;

export const main = async function (event: GetPlayerStatsEvent) {
  const { playerRepository } = event.context.repositories;
  const { gameworldId, playerId } = event.pathParameters;

  const player = await playerRepository.getPlayerWithStats(gameworldId, playerId);

  if (!player) {
    return buildResponse(404, JSON.stringify({ error: 'Player not found' }));
  }

  // Transform the data to return only what's needed
  const response = {
    playerId: player.playerId,
    gameworldId: player.gameworldId,
    firstName: player.firstName,
    surname: player.surname,
    age: player.age,
    value: player.value,
    energy: player.energy,
    lastMatchPlayed: player.lastMatchPlayed,
    injuredUntil: player.injuredUntil,
    suspendedForGames: player.suspendedForGames,
    isTransferListed: player.isTransferListed,
    retiringAtEndOfSeason: player.retiringAtEndOfSeason,
    overallStats: player.overallStats
      ? {
          yellowCards: player.overallStats.yellowCards,
          redCards: player.overallStats.redCards,
          passesCompleted: player.overallStats.passesCompleted,
          passesAttempted: player.overallStats.passesAttempted,
          successfulBallCarries: player.overallStats.successfulBallCarries,
          ballCarriesAttempted: player.overallStats.ballCarriesAttempted,
          shots: player.overallStats.shots,
          shotsOnTarget: player.overallStats.shotsOnTarget,
          goals: player.overallStats.goals,
          saves: player.overallStats.saves,
          tackles: player.overallStats.tackles,
          fouls: player.overallStats.fouls,
        }
      : null,
    matchHistory: player.matchHistory.getItems().map((match) => ({
      fixtureId: match.fixtureId,
      homeTeamName: match.fixture?.homeTeam.teamName,
      awayTeamName: match.fixture?.awayTeam.teamName,
      homeTeamScore: Number(match.fixture?.score?.[0]) ?? null,
      awayTeamScore: Number(match.fixture?.score?.[1]) ?? null,
      yellowCards: match.yellowCards,
      redCards: match.redCards,
      passesCompleted: match.passesCompleted,
      passesAttempted: match.passesAttempted,
      successfulBallCarries: match.successfulBallCarries,
      ballCarriesAttempted: match.ballCarriesAttempted,
      shots: match.shots,
      shotsOnTarget: match.shotsOnTarget,
      goals: match.goals,
      saves: match.saves,
      tackles: match.tackles,
      fouls: match.fouls,
    })),
  };

  return buildResponse(200, jsonStringifySafe(response));
};

export const handler = httpMiddify(main, {
  schema: getPlayerStatsSchema,
});
