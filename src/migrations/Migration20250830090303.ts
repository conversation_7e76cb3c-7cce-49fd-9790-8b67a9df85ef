import { Migration } from '@mikro-orm/migrations';

export class Migration20250830090303 extends Migration {

  override async up(): Promise<void> {
    // Clean up orphaned PlayerMatchHistory records that reference non-existent fixtures
    this.addSql(`
      DELETE FROM "player_match_history"
      WHERE "fixture_fixture_id" NOT IN (
        SELECT "fixture_id" FROM "fixture"
      );
    `);

    this.addSql(`alter table "transfer_list" alter column "created_at" type bigint using ("created_at"::bigint);`);
    this.addSql(`alter table "transfer_list" alter column "created_at" set default 1756544583148;`);
  }

  override async down(): Promise<void> {
    // No rollback for orphaned data cleanup - it's a one-way operation
    this.addSql(`alter table "transfer_list" alter column "created_at" type bigint using ("created_at"::bigint);`);
    this.addSql(`alter table "transfer_list" alter column "created_at" set default 1756502474637;`);
  }

}
