# Monolith Deployment Reliability & Hardening Backlog

This document captures potential next improvements for the monolith EC2 deployment pipeline beyond the current atomic symlink release + smoke test implementation.

## Current Baseline (Already Implemented)
- Atomic release directories: `/opt/monolith/releases/<release>` with `current` symlink.
- Pre‑cutover smoke test on alternate port (ephemeral) before swapping.
- Rollback to previous symlink on immediate post‑start failure.
- Basic release metadata (REVISION files) and pruning of old releases.
- Shared user-data script provisioning placeholder release.

## Backlog Items
These are optional but recommended for production‑grade robustness and operational clarity.

### 1. User-Data Readiness Health Loop
**Goal:** Ensure the initial placeholder service is actually listening before ALB / external checks start (reduces initial 502 / unhealthy target period).  
**Approach:** In `monolith-base.sh`, after starting systemd unit, poll `curl -sf http://127.0.0.1:3000/ping` up to N attempts (e.g. 30 * 1s). Log success/failure.  
**Edge Cases:** Service may start slowly if future migrations added; loop should tolerate transient failures but exit cleanly (script already finished, so optional best-effort).  
**Status:** Not implemented.

### 2. Parameterize Node.js Version
**Goal:** Simplify controlled upgrades of the runtime.  
**Approach:** Use a placeholder (e.g. `__NODE_MAJOR__`) in user-data script, replace via Pulumi (config driven: `pulumi config set monolith:nodeMajor 20`).  
**Detection:** Add Node version to `/opt/monolith/current/REVISION` or a separate `/opt/monolith/ENV_INFO` for debugging.  
**Status:** Not implemented (currently hard-coded to 20).

### 3. Systemd Hardening
**Goal:** Reduce blast radius if the process is compromised.  
**Additions (evaluate one by one):**
- `ProtectSystem=full`
- `ProtectHome=true`
- `NoNewPrivileges=yes`
- `PrivateTmp=yes`
- `RestrictSUIDSGID=yes`
- `AmbientCapabilities=` (empty)
- `LockPersonality=yes`
- `MemoryDenyWriteExecute=yes` (verify Node compatibility first – often OK)
- `ReadWritePaths=/opt/monolith` (and logs if needed)
**Status:** Not implemented.

### 4. Drift Detection via Checksum Comment
**Goal:** Detect manual drift of the shared user-data script to force refresh or raise visibility.  
**Approach:** During build pipeline, compute SHA256 of `monolith-base.sh` and inject as a comment (`# USERDATA_SHA256=<hash>`). Pulumi could include the hash in the EC2 launch template / userData value so any change triggers recreation or at minimum logs the difference.  
**Status:** Not implemented.

### 5. Centralized Environment File Management
**Goal:** Standardize and audit environment variables across releases; support safe rotation.  
**Approach:**
- Maintain canonical `/opt/monolith/shared/.env` (already used) but add a symlink creation step in user-data if missing.
- Record SHA256 of `.env` inside each release (e.g. `.env.sha256`) for forensic comparison.
- Optional: Add a guarded merge strategy (e.g. generate `.env.next` then swap only if hash differs and syntax passes simple validation).
**Status:** Partially implemented (shared file exists, hash audit not present).

### 6. /version Gating & Enforcement
**Goal:** Guarantee the service serving traffic matches the intended revision (not just alive).  
**Approach:** After systemctl start, poll both:
- `curl -sf http://127.0.0.1:3000/ping` equals full SHA.
- Optionally via CloudFront domain (external) to confirm CDN / ALB path is updated.
**Status:** Partially implemented (external call best-effort, not gating). Needs strict comparison & failure signaling.

### 7. Rollback Tooling & Metadata
**Goal:** Make manual rollback easy & safe beyond immediate auto‑rollback.  
**Approach:**
- Write `/opt/monolith/ROLLBACK_INFO` JSON: `{ prev:"/opt/monolith/releases/release-...", current:"/opt/monolith/releases/release-...", activatedAt:"ISO" }`.
- Install `/usr/local/bin/monolith-rollback.sh` (validates target exists, stops service, swaps symlink back, restarts, logs result).  
**Status:** Not implemented (only implicit previous pointer inside release).

### 8. Disk Space Preflight
**Goal:** Avoid partial deployments on nearly full root volume.  
**Approach:** Before extracting archive: check available KB (`df -Pk /opt` or `/`). Require `(archive_size * 2 + 200MB)` free. Abort gracefully if insufficient.  
**Status:** Not implemented (previous logic removed).

### 9. Enhanced Logging / Audit Trail
**Goal:** Improved traceability of deployments & rollbacks.  
**Approach:** Append structured JSON lines to `/var/log/monolith-deploy.log` at key milestones (prepare, smoke_pass, cutover, rollback, prune). Include release id, revision, duration, outcome.  
**Status:** Not implemented.

### 10. Metrics & Alerting for Deploy Success
**Goal:** Observability for deploy health.  
**Approach:** Emit a custom CloudWatch metric (namespace `MonolithDeployments`, metric `DeploySuccess` = 1 on success, 0 on rollback). Add alarm if no successes in N hours or a failure occurs.  
**Status:** Not implemented.

### 11. Optional Canary Warm-Up
**Goal:** Pre-warm caches or DB connections before cutover to reduce initial latency spike.  
**Approach:** In smoke test phase, hit a small set of representative internal endpoints (manager, leagues, etc.) before activation.  
**Status:** Not implemented.

### 12. Test Harness for Release Validity
**Goal:** Prevent runtime regressions that simple /ping misses.  
**Approach:** Include a minimal Node script (e.g. `scripts/release-smoke.mjs`) bundled into release to import critical modules and assert no thrown errors on initialization. Run it prior to ephemeral port launch.  
**Status:** Not implemented.

### 13. Graceful Draining (Future / Multi-Instance)
**Goal:** When horizontally scaled, allow in-flight requests to finish before shutdown.  
**Approach:** Add pre-stop script + `ExecStop` with a small sleep or connection draining logic; coordinate with ALB deregistration wait.  
**Status:** N/A single instance now; future readiness.

### 14. Security: Least Privilege Tightening
**Goal:** Reduce IAM surface area.  
**Approach:** Separate role statements per subsystem, remove wildcard Resource where possible (e.g. restrict Cognito to specific user pool ARNs, logs to specific log groups).  
**Status:** Broad policies currently; tightening deferred.

### 15. Automated Migration Safety (If DB migrations added later)
**Goal:** Prevent cutover if migrations fail or incomplete.  
**Approach:** Add step: run migrations inside new release before smoke test; rollback on failure.  
**Status:** Not implemented (no migrations run in deploy step currently).

## Prioritized Suggested Sequence
1. /ping gating & structured rollback metadata (Items 6 & 7)
2. Disk space preflight + deploy audit log (Items 8 & 9)
3. User-data readiness loop (Item 1) & systemd hardening (Item 3)
4. Rollout Node version parameterization (Item 2)
5. Environment hash + checksum drift detection (Items 4 & 5)
6. Metrics + alerting (Item 10)
7. Canary warm-up + advanced smoke harness (Items 11 & 12)

## Quick Implementation Notes
- All shell changes should live in dedicated scripts (avoid large heredocs inside TS for maintainability).
- Provide a dry-run mode for the deploy script to validate archive structure only.
- Keep release directories immutable post-activation (write only logs outside release tree) to preserve reproducibility.

## Revision
Generated backlog on: {{DATE-TIME}}

(Replace {{DATE-TIME}} via future automation if desired.)

