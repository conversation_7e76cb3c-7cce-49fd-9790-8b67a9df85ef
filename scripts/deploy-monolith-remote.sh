#!/bin/bash
set -euo pipefail

# This script runs on the EC2 instance to deploy a new monolith release
# It expects environment variables to be passed from the calling script

ARCHIVE=$(ls -1t ~/*.tgz | head -n1)
RELEASE_NAME=$(basename "$ARCHIVE" .tgz)
BASE_DIR=/opt/monolith
RELEASES_DIR=$BASE_DIR/releases
SHARED_DIR=$BASE_DIR/shared
CURRENT_LINK=$BASE_DIR/current

mkdir -p "$RELEASES_DIR" "$SHARED_DIR"

echo "Extracting $ARCHIVE to /tmp..."
cd /tmp
rm -rf "/tmp/$RELEASE_NAME" || true
tar -xzf "$ARCHIVE"
rm -f "$ARCHIVE"

# Move into releases
mv "/tmp/$RELEASE_NAME" "$RELEASES_DIR/" || { echo "Move failed" >&2; exit 1; }
cd "$RELEASES_DIR/$RELEASE_NAME"

echo "Installing production dependencies (deterministic)..."
npm ci --omit=dev --no-audit --no-fund --silent --cache /tmp/.npm

# Create revision metadata
echo "${GIT_REVISION}" > REVISION
echo "${SHORT_SHA}" > REVISION_SHORT

# Ensure env file exists; create atomically if not or if template changed
ENV_FILE="$SHARED_DIR/.env"
TMP_ENV="$SHARED_DIR/.env.new"

# Create the environment file with proper variable substitution
cat > "$TMP_ENV" <<EOF
NODE_ENV=production
PORT=3000
STAGE=stage
GIT_REVISION=${GIT_REVISION}
DATABASE_URL=${DATABASE_URL}
DATABASE_USER=postgres
DATABASE_PASSWORD=${DATABASE_PASSWORD}
DATABASE_NAME=postgres
DATABASE_PORT=5432
REGION=us-east-2
COGNITO_USER_POOL_ID=${COGNITO_USER_POOL_ID}
COGNITO_USER_POOL_CLIENT_ID=${COGNITO_USER_POOL_CLIENT_ID}
SENTRY_DSN=https://<EMAIL>/4509911419125840
EMAIL_QUEUE_URL=${EMAIL_QUEUE_URL}
INBOX_TABLE_NAME=${INBOX_TABLE_NAME}
FIXTURE_DETAIL_TABLE_NAME=${FIXTURE_DETAIL_TABLE_NAME}
TRACER_ENABLED=true
NOTION_ISSUE_TRACKER_DB_ID=${NOTION_ISSUE_TRACKER_DB_ID}
NOTION_API_KEY=${NOTION_API_KEY}
EOF

# Replace if different or missing
if [ ! -f "$ENV_FILE" ] || ! diff -q "$ENV_FILE" "$TMP_ENV" >/dev/null 2>&1; then
  mv "$TMP_ENV" "$ENV_FILE"
else
  rm -f "$TMP_ENV"
fi

# Symlink env into release
ln -sfn "$ENV_FILE" .env

echo "Starting smoke test on ephemeral port..."
export PORT=3101
export NODE_ENV=production
export STAGE=stage
(node monolith/server.js > /tmp/smoke.$RELEASE_NAME.log 2>&1 & echo $! > /tmp/smoke.$RELEASE_NAME.pid)

# Wait for health
ATTEMPTS=20
OK=false
for i in $(seq 1 $ATTEMPTS); do
  sleep 1
  if curl -sf http://127.0.0.1:3101/ping >/dev/null 2>&1; then
    OK=true
    break
  fi
done

PID=$(cat /tmp/smoke.$RELEASE_NAME.pid || true)
if [ -n "$PID" ]; then kill "$PID" >/dev/null 2>&1 || true; fi

if [ "$OK" != "true" ]; then
  echo "Smoke test failed for $RELEASE_NAME" >&2
  exit 2
fi

echo "Smoke test passed for $RELEASE_NAME"

# Prepare rollback info
PREVIOUS_TARGET=""
if [ -L "$CURRENT_LINK" ]; then
  PREVIOUS_TARGET=$(readlink -f "$CURRENT_LINK" || true)
fi
echo "$PREVIOUS_TARGET" > "$RELEASES_DIR/$RELEASE_NAME/.previous"

# Acquire deploy lock (use shared dir for writable lock file instead of /var/lock)
LOCK_FILE="$SHARED_DIR/monolith.deploy.lock"
exec 9>"$LOCK_FILE"
flock -n 9 || { echo "Another deployment in progress" >&2; exit 3; }

echo "Switching to new release atomically..."
systemctl stop monolith || true
ln -sfn "$RELEASES_DIR/$RELEASE_NAME" "$CURRENT_LINK"
systemctl start monolith

# Verify new service
ATTEMPTS=30
OK=false
for i in $(seq 1 $ATTEMPTS); do
  sleep 1
  if systemctl is-active --quiet monolith && curl -sf http://127.0.0.1:3000/ping >/dev/null 2>&1; then
    OK=true
    break
  fi
done

if [ "$OK" != "true" ]; then
  echo "Primary health check failed, rolling back" >&2
  if [ -n "$PREVIOUS_TARGET" ] && [ -d "$PREVIOUS_TARGET" ]; then
    systemctl stop monolith || true
    ln -sfn "$PREVIOUS_TARGET" "$CURRENT_LINK"
    systemctl start monolith || true
  fi
  exit 4
fi

echo "New release active: $RELEASE_NAME"

echo "Pruning old releases (keep 5)..."
cd "$RELEASES_DIR"
ls -1dt release-* 2>/dev/null | tail -n +6 | while read OLD; do
  [ "$OLD" != "$RELEASE_NAME" ] && rm -rf "$OLD" || true
done

echo "Release deployment complete."
