#!/bin/bash

# Deploy monolith to EC2 instance
# Usage: ./scripts/deploy-monolith.sh [stage] [instance-ip] [optional-key-file]
# OR: Called from GitHub Actions with environment variables set

set -euo pipefail

STAGE=${1:-stage}
INSTANCE_IP=${2:-}
USER_KEY_PATH=${3:-}

if [ -z "${INSTANCE_IP}" ]; then
    echo "Usage: $0 [stage] <instance-ip> [optional-key-file]" >&2
    exit 1
fi

echo "Args: stage='$STAGE' instance_ip='$INSTANCE_IP' user_key_arg='${USER_KEY_PATH}'"

echo "Deploying monolith to $STAGE environment at $INSTANCE_IP"

# Build application (full build so generated types + shared artifacts are present)
echo "Building application..."
npm run build
npm run monolith:build

echo "Creating deployment package..."
DEPLOY_DIR="deploy-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$DEPLOY_DIR"

# Copy built output + package manifests (do NOT include node_modules)
cp -r dist/* "$DEPLOY_DIR/"
cp package.json "$DEPLOY_DIR/"
cp package-lock.json "$DEPLOY_DIR/" 2>/dev/null || true
rm -rf "$DEPLOY_DIR/node_modules"

tar -czf "$DEPLOY_DIR.tar.gz" "$DEPLOY_DIR"
echo "Deployment package created: $DEPLOY_DIR.tar.gz"

# Determine SSH key
KEY_FILE=""
if [ -n "$USER_KEY_PATH" ]; then
  # Expand ~ if present
  case "$USER_KEY_PATH" in
    ~*) KEY_FILE="${USER_KEY_PATH/#~/$HOME}" ;;
    *) KEY_FILE="$USER_KEY_PATH" ;;
  esac
  # If relative and not found, try ./relative
  if [ ! -f "$KEY_FILE" ] && [ -f "./$USER_KEY_PATH" ]; then
    KEY_FILE="./$USER_KEY_PATH"
  fi
  if [ ! -f "$KEY_FILE" ]; then
    echo "Provided key path '$USER_KEY_PATH' not found (checked '$KEY_FILE'). Aborting." >&2
    exit 1
  fi
  echo "Using user-supplied key path: $KEY_FILE"
fi

if [ -z "$KEY_FILE" ]; then
  # Try Pulumi output (only if pulumi installed)
  if command -v pulumi >/dev/null 2>&1; then
    pushd infrastructure >/dev/null
    KEY_PAIR_NAME=$(pulumi stack output monolithKeyPairName 2>/dev/null || echo "")
    popd >/dev/null
    if [ -n "$KEY_PAIR_NAME" ]; then
      KEY_FILE="$HOME/.ssh/${KEY_PAIR_NAME}.pem"
      echo "Pulumi key pair name found: $KEY_PAIR_NAME"
    fi
  fi
fi

if [ -z "$KEY_FILE" ]; then
  KEY_FILE="$HOME/.ssh/${STAGE}-monolith-key.pem"
  echo "Falling back to default key path: $KEY_FILE"
fi

# If key file still doesn't exist, try to provision from repo root monolith-key
if [ ! -f "$KEY_FILE" ]; then
  if [ -f "./monolith-key" ]; then
    echo "Local key ./monolith-key found. Installing to $KEY_FILE"
    mkdir -p "$(dirname "$KEY_FILE")"
    cp ./monolith-key "$KEY_FILE"
  else
    echo "ERROR: SSH key file not found: $KEY_FILE (and ./monolith-key missing)" >&2
    echo "Provide key as 3rd arg or place monolith-key in repo root." >&2
    exit 1
  fi
fi

# Normalize line endings (in case checked out with CRLF) and set perms
if grep -q $'\r' "$KEY_FILE" 2>/dev/null; then
  sed -i 's/\r$//' "$KEY_FILE"
fi
chmod 600 "$KEY_FILE" || true

echo "Using SSH key: $KEY_FILE"

echo "Pre-flight: remote disk space + prune old backups before upload";
# Build archive size (KB) for comparison
ARCHIVE_SIZE_BYTES=$(stat -c%s "$DEPLOY_DIR.tar.gz")
ARCHIVE_SIZE_KB=$(( (ARCHIVE_SIZE_BYTES + 1023) / 1024 ))

# Fetch remote /tmp available space (KB) and prune old backups first to free space
REMOTE_INFO=$(ssh -i "$KEY_FILE" -o StrictHostKeyChecking=no ec2-user@"$INSTANCE_IP" "set -e;
  # Prune old backups (keep last 2) BEFORE upload if low space
  mapfile -t BK < <(ls -1t /opt 2>/dev/null | grep '^monolith.backup.' || true);
  if [ \${#BK[@]} -gt 2 ]; then
    echo 'Pre-upload pruning old backups:'; for OLD in \"\${BK[@]:2}\"; do sudo rm -rf /opt/\$OLD || true; echo removed:/opt/\$OLD; done; fi;
  df -Pk /tmp | awk 'NR==2 {print \$4}'") || { echo "Could not SSH to perform pre-flight check" >&2; exit 1; }
REMOTE_AVAIL_KB=$REMOTE_INFO

if [[ ! $REMOTE_AVAIL_KB =~ ^[0-9]+$ ]]; then
  echo "Warning: Could not parse remote /tmp free space ('$REMOTE_AVAIL_KB'). Continuing without check." >&2
else
  echo "Archive size: ${ARCHIVE_SIZE_KB}KB, Remote /tmp free: ${REMOTE_AVAIL_KB}KB";
  SAFETY_KB=$(( ARCHIVE_SIZE_KB + 10240 ))
  if [ $REMOTE_AVAIL_KB -lt $SAFETY_KB ]; then
    echo "ERROR: Not enough space on remote /tmp (need >= ${SAFETY_KB}KB incl. 10MB buffer)." >&2
    echo "Free space by pruning more backups or enlarging volume, then retry." >&2
    exit 1
  fi
fi

echo "Uploading deployment package..."
scp -i "$KEY_FILE" -o StrictHostKeyChecking=no "$DEPLOY_DIR.tar.gz" ec2-user@"$INSTANCE_IP":/tmp/ || { echo "Upload failed" >&2; exit 1; }

echo "Deploying on remote server..."
ssh -i "$KEY_FILE" -o StrictHostKeyChecking=no ec2-user@"$INSTANCE_IP" <<EOF
  set -euo pipefail
  echo '--- Disk usage before deployment ---'
  df -h /
  df -h /tmp || true

  sudo systemctl stop monolith || true

  # Prune old backups (keep last 3)
  mapfile -t BACKUPS < <(ls -1t /opt 2>/dev/null | grep '^monolith.backup.' || true)
  if [ \${#BACKUPS[@]} -gt 3 ]; then
    echo "Pruning old backups:";
    for OLD in "\${BACKUPS[@]:3}"; do
      echo "Removing /opt/\$OLD";
      sudo rm -rf "/opt/\$OLD" || true;
    done
  fi

  cd /tmp
  tar -xzf "$DEPLOY_DIR.tar.gz"
  sudo rm -f "$DEPLOY_DIR.tar.gz"

  # Backup existing deployment AFTER extraction success
  if [ -d /opt/monolith ]; then
    sudo mv /opt/monolith /opt/monolith.backup.\$(date +%Y%m%d-%H%M%S)
  fi

  sudo mv "$DEPLOY_DIR" /opt/monolith
  sudo chown -R ec2-user:ec2-user /opt/monolith

  cd /opt/monolith
  echo 'Installing production dependencies (remote)...'

  # Check available memory
  FREE_MEM=\$(free -m | awk 'NR==2{printf "%.0f", \$7}')
  echo "Available memory: \${FREE_MEM}MB"

  # Create temporary swap if memory is low (less than 1GB free)
  SWAP_CREATED=false
  if [ "\$FREE_MEM" -lt 1000 ]; then
    echo "Low memory detected. Creating temporary swap space..."
    if ! swapon --show | grep -q /tmp/swapfile; then
      sudo fallocate -l 2G /tmp/swapfile || sudo dd if=/dev/zero of=/tmp/swapfile bs=1M count=2048
      sudo chmod 600 /tmp/swapfile
      sudo mkswap /tmp/swapfile
      sudo swapon /tmp/swapfile
      SWAP_CREATED=true
      echo "Temporary swap space created"
    fi
  fi

  # Install dependencies with memory-friendly options
  export NODE_OPTIONS="--max-old-space-size=512"

  # Try npm ci first, fallback to npm install if it fails
  if ! npm ci --production --silent --prefer-offline --no-audit --no-fund --maxsockets 1; then
    echo "npm ci failed, trying npm install as fallback..."
    rm -rf node_modules package-lock.json 2>/dev/null || true
    npm install --production --silent --prefer-offline --no-audit --no-fund --maxsockets 1
  fi

  # Clean up swap if we created it
  if [ "\$SWAP_CREATED" = true ]; then
    echo "Cleaning up temporary swap space..."
    sudo swapoff /tmp/swapfile || true
    sudo rm -f /tmp/swapfile || true
    echo "Temporary swap space removed"
  fi

  # Environment variables (extend as needed)
  sudo tee /opt/monolith/.env > /dev/null << EOL
NODE_ENV=production
PORT=3000
STAGE=$STAGE
# DATABASE_URL=your-database-url
# COGNITO_USER_POOL_ID=your-user-pool-id
# COGNITO_USER_POOL_CLIENT_ID=your-client-id
EOL

  sudo tee /etc/systemd/system/monolith.service > /dev/null << EOL
[Unit]
Description=Jumpers for Goalposts Monolith API
After=network.target

[Service]
Type=simple
User=ec2-user
WorkingDirectory=/opt/monolith
ExecStart=/usr/bin/node monolith/server.cjs
Restart=always
RestartSec=10
EnvironmentFile=/opt/monolith/.env
StandardOutput=append:/var/log/monolith.log
StandardError=append:/var/log/monolith.log

[Install]
WantedBy=multi-user.target
EOL

  sudo systemctl daemon-reload
  sudo systemctl enable monolith
  sudo systemctl start monolith
  sleep 8
  sudo systemctl status monolith --no-pager || (echo 'Service failed; recent logs:' && sudo journalctl -u monolith --no-pager -n 80 && exit 1)
  echo '--- Disk usage after deployment ---'
  df -h /
  echo 'Deployment completed.'
EOF

# Local cleanup
rm -rf "$DEPLOY_DIR" "$DEPLOY_DIR.tar.gz"

echo "Deployment script completed!"
echo "Check status: ssh -i $KEY_FILE ec2-user@$INSTANCE_IP 'sudo systemctl status monolith'"
echo "Tail logs:   ssh -i $KEY_FILE ec2-user@$INSTANCE_IP 'sudo journalctl -u monolith -f'"
