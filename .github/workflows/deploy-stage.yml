name: Deploy to Stage

on:
  workflow_run:
    workflows:
      - Run Tests
    types:
      - completed
    branches:
      - main

concurrency:
  group: staging-environment
  cancel-in-progress: false

jobs:
  deploy:
    if: ${{ github.event.workflow_run.conclusion == 'success' }}
    runs-on: ubuntu-latest
    environment: staging

    steps:
      - uses: actions/checkout@v4

      - name: Get git revision
        id: git_revision
        run: |
          echo "git_revision=$(git rev-parse HEAD)" >> $GITHUB_OUTPUT
          echo "short_sha=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies (root)
        run: npm ci

      - name: Build application
        run: npm run build

      - name: Install dependencies (infrastructure)
        working-directory: ./infrastructure
        run: npm ci

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Setup Pulumi
        uses: pulumi/actions@v4

      - name: Deploy infrastructure to stage
        working-directory: ./infrastructure
        run: pulumi up --yes --non-interactive --skip-preview --stack stage
        env:
          PULUMI_ACCESS_TOKEN: ${{ secrets.PULUMI_ACCESS_TOKEN }}

      - name: Build monolith application
        run: npm run monolith:build

      - name: Get deployment information
        id: get-deployment-info
        working-directory: ./infrastructure
        run: |
          INSTANCE_IP=$(pulumi stack output monolithPublicIp --stack stage 2>/dev/null || echo "")
          CLOUDFRONT_DOMAIN=$(pulumi stack output cloudFrontDistributionDomain --stack stage 2>/dev/null || echo "")

          if [ -z "$INSTANCE_IP" ]; then
            echo "No monolith instance found, skipping deployment"
            echo "skip_deployment=true" >> $GITHUB_OUTPUT
          else
            echo "instance_ip=$INSTANCE_IP" >> $GITHUB_OUTPUT
            echo "skip_deployment=false" >> $GITHUB_OUTPUT
            echo "cloudfront_domain=$CLOUDFRONT_DOMAIN" >> $GITHUB_OUTPUT

            echo "🚀 Deployment Information:"
            echo "EC2 Instance IP: $INSTANCE_IP"
            echo "CloudFront Domain: $CLOUDFRONT_DOMAIN"
            echo "Test URL: https://$CLOUDFRONT_DOMAIN/ping"
          fi
        env:
          PULUMI_ACCESS_TOKEN: ${{ secrets.PULUMI_ACCESS_TOKEN }}

      - name: Deploy monolith to EC2 (atomic release)
        if: steps.get-deployment-info.outputs.skip_deployment == 'false'
        run: |
          set -euo pipefail
          RELEASE_ID="release-$(date +%Y%m%d-%H%M%S)-${{ steps.git_revision.outputs.short_sha }}"
          echo "Preparing $RELEASE_ID"
          WORK_DIR="$RELEASE_ID"
          mkdir -p "$WORK_DIR"

          # Copy build output & essentials
          cp -r dist-monolith/* "$WORK_DIR/"
          cp package.json "$WORK_DIR/"
          cp package-lock.json "$WORK_DIR/"
          rm -rf "$WORK_DIR/node_modules"

          tar -czf "$WORK_DIR.tgz" "$WORK_DIR"

          # SSH key setup
          mkdir -p ~/.ssh
          echo "${{ secrets.EC2_PRIVATE_KEY }}" > ~/.ssh/monolith-key.pem
          sed -i 's/\r$//' ~/.ssh/monolith-key.pem
          chmod 600 ~/.ssh/monolith-key.pem

          TARGET=${{ steps.get-deployment-info.outputs.instance_ip }}

          echo "Uploading archive..."
          scp -i ~/.ssh/monolith-key.pem -o StrictHostKeyChecking=no "$WORK_DIR.tgz" ec2-user@$TARGET:~/

          echo "Running remote deployment (prepare new release without downtime)..."
          ssh -i ~/.ssh/monolith-key.pem -o StrictHostKeyChecking=no ec2-user@$TARGET <<'REMOTE_PREP'
            set -euo pipefail
            ARCHIVE=$(ls -1t ~/*.tgz | head -n1)
            RELEASE_NAME=$(basename "$ARCHIVE" .tgz)
            BASE_DIR=/opt/monolith
            RELEASES_DIR=$BASE_DIR/releases
            SHARED_DIR=$BASE_DIR/shared
            CURRENT_LINK=$BASE_DIR/current

            mkdir -p "$RELEASES_DIR" "$SHARED_DIR"

            echo "Extracting $ARCHIVE to /tmp..."
            cd /tmp
            rm -rf "/tmp/$RELEASE_NAME" || true
            tar -xzf "$ARCHIVE"
            rm -f "$ARCHIVE"

            # Move into releases
            mv "/tmp/$RELEASE_NAME" "$RELEASES_DIR/" || { echo "Move failed" >&2; exit 1; }
            cd "$RELEASES_DIR/$RELEASE_NAME"

            echo "Installing production dependencies (deterministic)..."
            npm ci --omit=dev --no-audit --no-fund --silent --cache /tmp/.npm

            # Create revision metadata
            echo "${{ steps.git_revision.outputs.git_revision }}" > REVISION
            echo "${{ steps.git_revision.outputs.short_sha }}" > REVISION_SHORT

            # Ensure env file exists; create atomically if not or if template changed
            ENV_FILE="$SHARED_DIR/.env"
            TMP_ENV="$SHARED_DIR/.env.new"
            cat > "$TMP_ENV" <<EOF_ENV
            NODE_ENV=production
            PORT=3000
            STAGE=stage
            GIT_REVISION=${{ steps.git_revision.outputs.git_revision }}
            DATABASE_URL=${{ secrets.DATABASE_URL }}
            DATABASE_USER=postgres
            DATABASE_PASSWORD=${{ secrets.DATABASE_PASSWORD }}
            DATABASE_NAME=postgres
            DATABASE_PORT=5432
            REGION=us-east-2
            COGNITO_USER_POOL_ID=${{ secrets.COGNITO_USER_POOL_ID }}
            COGNITO_USER_POOL_CLIENT_ID=${{ secrets.COGNITO_USER_POOL_CLIENT_ID }}
            SENTRY_DSN=https://<EMAIL>/4509911419125840
            EMAIL_QUEUE_URL=${{ vars.EMAIL_QUEUE_URL }}
            INBOX_TABLE_NAME=${{ vars.INBOX_TABLE_NAME }}
            FIXTURE_DETAIL_TABLE_NAME=${{ vars.FIXTURE_DETAIL_TABLE_NAME }}
            TRACER_ENABLED=true
            NOTION_ISSUE_TRACKER_DB_ID=${{ secrets.NOTION_ISSUE_TRACKER_DB_ID }}
            NOTION_API_KEY=${{ secrets.NOTION_API_KEY }}
            EOF_ENV
            # Replace if different or missing
            if [ ! -f "$ENV_FILE" ] || ! diff -q "$ENV_FILE" "$TMP_ENV" >/dev/null 2>&1; then
              mv "$TMP_ENV" "$ENV_FILE"
            else
              rm -f "$TMP_ENV"
            fi

            # Symlink env into release
            ln -sfn "$ENV_FILE" .env

            echo "Starting smoke test on ephemeral port..."
            export PORT=3101
            export NODE_ENV=production
            export STAGE=stage
            (node monolith/server.js > /tmp/smoke.$RELEASE_NAME.log 2>&1 & echo $! > /tmp/smoke.$RELEASE_NAME.pid)

            # Wait for health
            ATTEMPTS=20
            OK=false
            for i in $(seq 1 $ATTEMPTS); do
              sleep 1
              if curl -sf http://127.0.0.1:3101/ping >/dev/null 2>&1; then
                OK=true
                break
              fi
            done

            PID=$(cat /tmp/smoke.$RELEASE_NAME.pid || true)
            if [ -n "$PID" ]; then kill "$PID" >/dev/null 2>&1 || true; fi

            if [ "$OK" != "true" ]; then
              echo "Smoke test failed for $RELEASE_NAME" >&2
              exit 2
            fi

            echo "Smoke test passed for $RELEASE_NAME"

            # Prepare rollback info
            PREVIOUS_TARGET=""
            if [ -L "$CURRENT_LINK" ]; then
              PREVIOUS_TARGET=$(readlink -f "$CURRENT_LINK" || true)
            fi
            echo "$PREVIOUS_TARGET" > "$RELEASES_DIR/$RELEASE_NAME/.previous"

            # Acquire deploy lock
            exec 9>/var/lock/monolith.deploy
            flock -n 9 || { echo "Another deployment in progress" >&2; exit 3; }

            echo "Switching to new release atomically..."
            systemctl stop monolith || true
            ln -sfn "$RELEASES_DIR/$RELEASE_NAME" "$CURRENT_LINK"
            systemctl start monolith

            # Verify new service
            ATTEMPTS=30
            OK=false
            for i in $(seq 1 $ATTEMPTS); do
              sleep 1
              if systemctl is-active --quiet monolith && curl -sf http://127.0.0.1:3000/ping >/dev/null 2>&1; then
                OK=true
                break
              fi
            done

            if [ "$OK" != "true" ]; then
              echo "Primary health check failed, rolling back" >&2
              if [ -n "$PREVIOUS_TARGET" ] && [ -d "$PREVIOUS_TARGET" ]; then
                systemctl stop monolith || true
                ln -sfn "$PREVIOUS_TARGET" "$CURRENT_LINK"
                systemctl start monolith || true
              fi
              exit 4
            fi

            echo "New release active: $RELEASE_NAME"

            echo "Pruning old releases (keep 5)..."
            cd "$RELEASES_DIR"
            ls -1dt release-* 2>/dev/null | tail -n +6 | while read OLD; do
              [ "$OLD" != "$RELEASE_NAME" ] && rm -rf "$OLD" || true
            done

            echo "Release deployment complete."
            REMOTE_PREP

          echo "Validating externally exposed /version endpoint via CloudFront (best-effort)..."
          if [ -n "${{ steps.get-deployment-info.outputs.cloudfront_domain }}" ]; then
            curl -fsS --retry 5 --retry-delay 2 "https://${{ steps.get-deployment-info.outputs.cloudfront_domain }}/version" || echo "External version check failed (non-fatal)"
          fi

          # Cleanup local artifacts
          rm -rf "$WORK_DIR" "$WORK_DIR.tgz" ~/.ssh/monolith-key.pem

      - name: Report deployed revision
        if: steps.get-deployment-info.outputs.skip_deployment == 'false'
        run: |
          echo "Deployed revision: ${{ steps.git_revision.outputs.git_revision }} (short: ${{ steps.git_revision.outputs.short_sha }})"
