name: Deploy Monolith Only

on:
  workflow_dispatch:
    inputs:
      instance_ip:
        description: 'EC2 Instance IP (optional - will auto-detect if not provided)'
        required: false
        type: string

concurrency:
  group: monolith-deployment
  cancel-in-progress: false

jobs:
  deploy-monolith:
    runs-on: ubuntu-latest
    environment: staging

    steps:
      - uses: actions/checkout@v4

      - name: Get git revision
        id: git_revision
        run: |
          echo "git_revision=$(git rev-parse HEAD)" >> $GITHUB_OUTPUT
          echo "short_sha=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies (root)
        run: npm ci

      - name: Build application
        run: npm run build

      - name: Build monolith application
        run: npm run monolith:build

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Setup Pulumi
        uses: pulumi/actions@v4

      - name: Get instance IP
        id: get-instance-ip
        working-directory: ./infrastructure
        run: |
          if [ -n "${{ github.event.inputs.instance_ip }}" ]; then
            INSTANCE_IP="${{ github.event.inputs.instance_ip }}"
            echo "Using provided instance IP: $INSTANCE_IP"
          else
            # Auto-detect instance IP
            INSTANCE_ID=$(pulumi stack output monolithInstanceId --stack stage 2>/dev/null || echo "")
            if [ -n "$INSTANCE_ID" ]; then
              INSTANCE_IP=$(aws ec2 describe-instances --instance-ids "$INSTANCE_ID" --query 'Reservations[0].Instances[0].PublicIpAddress' --output text 2>/dev/null || echo "")
            else
              # Fallback to Pulumi output
              INSTANCE_IP=$(pulumi stack output monolithPublicIp --stack stage 2>/dev/null || echo "")
            fi
            echo "Auto-detected instance IP: $INSTANCE_IP"
          fi
          
          if [ -z "$INSTANCE_IP" ] || [ "$INSTANCE_IP" = "None" ]; then
            echo "ERROR: Could not determine instance IP" >&2
            exit 1
          fi
          
          echo "instance_ip=$INSTANCE_IP" >> $GITHUB_OUTPUT
        env:
          PULUMI_ACCESS_TOKEN: ${{ secrets.PULUMI_ACCESS_TOKEN }}

      - name: Deploy monolith to EC2 (atomic release)
        run: |
          set -euo pipefail
          RELEASE_ID="release-$(date +%Y%m%d-%H%M%S)-${{ steps.git_revision.outputs.short_sha }}"
          echo "Preparing $RELEASE_ID"
          WORK_DIR="$RELEASE_ID"
          mkdir -p "$WORK_DIR"

          # Copy build output & essentials
          cp -r dist-monolith/* "$WORK_DIR/"
          cp package.json "$WORK_DIR/"
          cp package-lock.json "$WORK_DIR/"
          rm -rf "$WORK_DIR/node_modules"

          tar -czf "$WORK_DIR.tgz" "$WORK_DIR"

          # SSH key setup
          mkdir -p ~/.ssh
          echo "${{ secrets.EC2_PRIVATE_KEY }}" > ~/.ssh/monolith-key.pem
          sed -i 's/\r$//' ~/.ssh/monolith-key.pem
          chmod 600 ~/.ssh/monolith-key.pem

          TARGET=${{ steps.get-instance-ip.outputs.instance_ip }}

          echo "Waiting for SSH to become ready at $TARGET ..."
          for i in {1..30}; do
            if ssh -i ~/.ssh/monolith-key.pem -o StrictHostKeyChecking=no -o BatchMode=yes -o ConnectTimeout=5 ec2-user@$TARGET 'echo ok' 2>/dev/null; then
              echo "SSH ready (attempt $i)"
              break
            fi
            echo "SSH not ready yet (attempt $i); sleeping 10s"
            sleep 10
            if [ "$i" -eq 30 ]; then
              echo "ERROR: SSH not ready after 30 attempts (~5 min)" >&2
              exit 1
            fi
          done

          upload_with_retry() {
            local SRC=$1
            local DEST=$2
            for attempt in {1..5}; do
              scp -i ~/.ssh/monolith-key.pem -o StrictHostKeyChecking=no -o ConnectTimeout=10 "$SRC" ec2-user@$TARGET:"$DEST" && return 0
              echo "Retry $attempt for $SRC failed; sleeping 5s" >&2
              sleep 5
              if [ "$attempt" -eq 5 ]; then
                echo "ERROR: Failed to upload $SRC after 5 attempts" >&2
                return 1
              fi
            done
          }

          echo "Uploading archive..."
          upload_with_retry "$WORK_DIR.tgz" "~/$(basename "$WORK_DIR.tgz")"

          echo "Uploading deployment script..."
          upload_with_retry scripts/deploy-monolith-remote.sh "~/deploy-monolith-remote.sh"

          echo "Running remote deployment (prepare new release without downtime)..."
          ssh -i ~/.ssh/monolith-key.pem -o StrictHostKeyChecking=no ec2-user@$TARGET \
            "export GIT_REVISION='${{ steps.git_revision.outputs.git_revision }}' && \
             export SHORT_SHA='${{ steps.git_revision.outputs.short_sha }}' && \
             export DATABASE_URL='${{ secrets.DATABASE_URL }}' && \
             export DATABASE_PASSWORD='${{ secrets.DATABASE_PASSWORD }}' && \
             export COGNITO_USER_POOL_ID='${{ secrets.COGNITO_USER_POOL_ID }}' && \
             export COGNITO_USER_POOL_CLIENT_ID='${{ secrets.COGNITO_USER_POOL_CLIENT_ID }}' && \
             export EMAIL_QUEUE_URL='${{ vars.EMAIL_QUEUE_URL }}' && \
             export INBOX_TABLE_NAME='${{ vars.INBOX_TABLE_NAME }}' && \
             export FIXTURE_DETAIL_TABLE_NAME='${{ vars.FIXTURE_DETAIL_TABLE_NAME }}' && \
             export NOTION_ISSUE_TRACKER_DB_ID='${{ secrets.NOTION_ISSUE_TRACKER_DB_ID }}' && \
             export NOTION_API_KEY='${{ secrets.NOTION_API_KEY }}' && \
             chmod +x ~/deploy-monolith-remote.sh && \
             ~/deploy-monolith-remote.sh"

          # Cleanup local artifacts
          rm -rf "$WORK_DIR" "$WORK_DIR.tgz" ~/.ssh/monolith-key.pem

      - name: Report deployed revision
        run: |
          echo "✅ Successfully deployed revision: ${{ steps.git_revision.outputs.git_revision }} (short: ${{ steps.git_revision.outputs.short_sha }})"
          echo "🚀 Instance IP: ${{ steps.get-instance-ip.outputs.instance_ip }}"
